import { Dimensions, PixelRatio } from 'react-native';

// Get screen dimensions
const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } = Dimensions.get('window');

// Base dimensions (iPhone 12 Pro as reference)
const BASE_WIDTH = 390;
const BASE_HEIGHT = 844;

/**
 * Responsive width based on screen width
 */
export const wp = (percentage: number): number => {
  const value = (percentage * SCREEN_WIDTH) / 100;
  return Math.round(PixelRatio.roundToNearestPixel(value));
};

/**
 * Responsive height based on screen height
 */
export const hp = (percentage: number): number => {
  const value = (percentage * SCREEN_HEIGHT) / 100;
  return Math.round(PixelRatio.roundToNearestPixel(value));
};

/**
 * Responsive font size based on screen width
 */
export const rf = (size: number): number => {
  const scale = SCREEN_WIDTH / BASE_WIDTH;
  const newSize = size * scale;
  return Math.round(PixelRatio.roundToNearestPixel(newSize));
};

/**
 * Responsive spacing (padding, margin) based on screen width
 */
export const rs = (size: number): number => {
  const scale = SCREEN_WIDTH / BASE_WIDTH;
  const newSize = size * scale;
  return Math.round(PixelRatio.roundToNearestPixel(newSize));
};

/**
 * Get responsive dimensions for containers
 */
export const getResponsiveDimensions = () => {
  const isSmallScreen = SCREEN_WIDTH < 375;
  const isMediumScreen = SCREEN_WIDTH >= 375 && SCREEN_WIDTH < 414;
  const isLargeScreen = SCREEN_WIDTH >= 414;

  return {
    isSmallScreen,
    isMediumScreen,
    isLargeScreen,
    screenWidth: SCREEN_WIDTH,
    screenHeight: SCREEN_HEIGHT,
    
    // Container dimensions
    cardPadding: isSmallScreen ? rs(12) : rs(15),
    sectionPadding: isSmallScreen ? rs(15) : rs(20),
    headerPadding: isSmallScreen ? rs(15) : rs(20),
    
    // Font sizes
    headerFontSize: isSmallScreen ? rf(18) : rf(20),
    titleFontSize: isSmallScreen ? rf(16) : rf(18),
    bodyFontSize: isSmallScreen ? rf(14) : rf(16),
    captionFontSize: isSmallScreen ? rf(11) : rf(12),
    smallFontSize: isSmallScreen ? rf(9) : rf(10),
    
    // Heights
    headerHeight: isSmallScreen ? hp(6) : hp(7),
    cardHeight: isSmallScreen ? hp(10) : hp(12),
    buttonHeight: isSmallScreen ? hp(5.5) : hp(6),
    inputHeight: isSmallScreen ? hp(5) : hp(5.5),
    
    // Margins and spacing
    sectionMargin: isSmallScreen ? rs(10) : rs(15),
    itemMargin: isSmallScreen ? rs(8) : rs(10),
    smallMargin: isSmallScreen ? rs(4) : rs(5),
  };
};

/**
 * Typography scale for consistent font sizing
 */
export const typography = {
  // Headers
  h1: rf(28),
  h2: rf(24),
  h3: rf(20),
  h4: rf(18),
  h5: rf(16),
  h6: rf(14),
  
  // Body text
  body1: rf(16),
  body2: rf(14),
  
  // Small text
  caption: rf(12),
  overline: rf(10),
  
  // Button text
  button: rf(16),
  
  // Input text
  input: rf(16),
};

/**
 * Spacing scale for consistent margins and padding
 */
export const spacing = {
  xs: rs(4),
  sm: rs(8),
  md: rs(12),
  lg: rs(16),
  xl: rs(20),
  xxl: rs(24),
  xxxl: rs(32),
};

/**
 * Border radius scale
 */
export const borderRadius = {
  xs: rs(4),
  sm: rs(6),
  md: rs(8),
  lg: rs(12),
  xl: rs(16),
  xxl: rs(20),
  round: rs(50),
};

/**
 * Icon sizes
 */
export const iconSizes = {
  xs: rf(12),
  sm: rf(16),
  md: rf(20),
  lg: rf(24),
  xl: rf(28),
  xxl: rf(32),
};

/**
 * Get device type
 */
export const getDeviceType = () => {
  if (SCREEN_WIDTH < 375) return 'small';
  if (SCREEN_WIDTH < 414) return 'medium';
  return 'large';
};

/**
 * Check if device is tablet
 */
export const isTablet = () => {
  const aspectRatio = SCREEN_HEIGHT / SCREEN_WIDTH;
  return aspectRatio < 1.6 && SCREEN_WIDTH > 600;
};

/**
 * Get safe minimum touch target size
 */
export const getMinTouchTarget = () => {
  return Math.max(44, rs(44)); // iOS HIG minimum 44pt
};

export default {
  wp,
  hp,
  rf,
  rs,
  getResponsiveDimensions,
  typography,
  spacing,
  borderRadius,
  iconSizes,
  getDeviceType,
  isTablet,
  getMinTouchTarget,
  SCREEN_WIDTH,
  SCREEN_HEIGHT,
};
